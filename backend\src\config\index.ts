import dotenv from 'dotenv';
import path from 'path';

// Загружаем переменные окружения
dotenv.config({
  path: path.join(__dirname, '../../.env')
});

export const config = {
  // Основные настройки приложения
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  corsOrigin: process.env.CORS_ORIGIN || (() => {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('CORS_ORIGIN must be explicitly set in production environment');
    }
    console.warn('⚠️  WARNING: Using permissive CORS in development. Set CORS_ORIGIN environment variable.');
    return ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080'];
  })(),

  // База данных
  database: {
    url: process.env.DATABASE_URL,
    pool: {
      min: parseInt(process.env.DB_POOL_MIN || '2', 10),
      max: parseInt(process.env.DB_POOL_MAX || '10', 10)
    }
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL,
    ttl: parseInt(process.env.REDIS_TTL || '3600', 10)
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || (() => {
      if (process.env.NODE_ENV === 'production') {
        throw new Error('JWT_SECRET must be set in production environment');
      }
      console.warn('⚠️  WARNING: Using default JWT secret in development. Set JWT_SECRET environment variable.');
      return 'dev-secret-key-change-in-production';
    })(),
    expiresIn: process.env.JWT_EXPIRES_IN || '1d'
  },

  // AWS
  aws: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
    bucket: process.env.AWS_BUCKET
  },

  // Email
  email: {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  },

  // Социальная аутентификация
  auth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET
    },
    facebook: {
      appId: process.env.FACEBOOK_APP_ID,
      appSecret: process.env.FACEBOOK_APP_SECRET
    }
  },

  // Push уведомления
  push: {
    firebase: {
      serverKey: process.env.FIREBASE_SERVER_KEY
    },
    apple: {
      teamId: process.env.APPLE_TEAM_ID,
      keyId: process.env.APPLE_KEY_ID,
      privateKey: process.env.APPLE_PRIVATE_KEY
    }
  },

  // Мониторинг
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN,
    newRelicKey: process.env.NEW_RELIC_LICENSE_KEY
  },

  // API ключи
  apiKeys: {
    openai: process.env.OPENAI_API_KEY,
    googleCloud: process.env.GOOGLE_CLOUD_API_KEY
  },

  // Feature flags
  features: {
    autoModeration: process.env.ENABLE_AUTO_MODERATION === 'true',
    realTimeNotifications: process.env.ENABLE_REAL_TIME_NOTIFICATIONS === 'true',
    analytics: process.env.ENABLE_ANALYTICS === 'true'
  },

  // Кэширование
  cache: {
    ttl: parseInt(process.env.CACHE_TTL || '3600', 10),
    maxSize: parseInt(process.env.MAX_CACHE_SIZE || '1000', 10)
  },

  // Rate limiting
  rateLimit: {
    window: process.env.RATE_LIMIT_WINDOW || '15m',
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
  },

  // Безопасность
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '10', 10),
    enable2FA: process.env.ENABLE_2FA === 'true',
    sessionSecret: process.env.SESSION_SECRET
  },

  // Логирование
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true',
    enableQueryLogging: process.env.ENABLE_QUERY_LOGGING === 'true'
  },

  // Тестирование
  test: {
    userEmail: process.env.TEST_USER_EMAIL,
    userPassword: process.env.TEST_USER_PASSWORD
  },

  // Метрики
  metrics: {
    enablePrometheus: process.env.ENABLE_PROMETHEUS_METRICS === 'true',
    port: parseInt(process.env.METRICS_PORT || '9090', 10)
  },

  // Content Delivery
  cdn: {
    url: process.env.CDN_URL,
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10) // 10MB
  },

  // Модерация
  moderation: {
    autoThreshold: parseFloat(process.env.AUTO_MODERATION_THRESHOLD || '0.8'),
    queueSize: parseInt(process.env.MODERATION_QUEUE_SIZE || '1000', 10),
    appealWindowDays: parseInt(process.env.APPEAL_WINDOW_DAYS || '30', 10)
  },

  // Аналитика
  analytics: {
    retentionDays: parseInt(process.env.ANALYTICS_RETENTION_DAYS || '90', 10),
    enableRealTime: process.env.ENABLE_REAL_TIME_ANALYTICS === 'true'
  },

  // Инфраструктура
  infrastructure: {
    kubernetesNamespace: process.env.KUBERNETES_NAMESPACE,
    dockerRegistry: process.env.DOCKER_REGISTRY
  }
};
