export interface User {
  id: string;
  email: string;
  phone?: string;
  firstName: string;
  birthDate: Date;
  gender: 'male' | 'female' | 'other';
  lookingFor: ('male' | 'female' | 'other')[];
  bio?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  photos: Photo[];
  interests: Interest[];
  isVerified: boolean;
  isPremium: boolean;
  settings: UserSettings;
  lastActive: Date;
  createdAt: Date;
}

export interface Photo {
  id: string;
  userId: string;
  url: string;
  isPrimary: boolean;
  createdAt: Date;
}

export interface Interest {
  id: string;
  name: string;
}

export interface UserSettings {
  ageRange: {
    min: number;
    max: number;
  };
  distance: number;
  notifications: {
    matches: boolean;
    messages: boolean;
    dateProposals: boolean;
    marketing: boolean;
  };
  privacy: {
    showOnline: boolean;
    showDistance: boolean;
    showAge: boolean;
  };
}

export interface Venue {
  id: string;
  name: string;
  type: VenueType;
  address: string;
  location: {
    latitude: number;
    longitude: number;
  };
  description?: string;
  photos: string[];
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
    };
  };
  contactInfo: {
    phone?: string;
    email?: string;
    website?: string;
  };
  rating: number;
  priceRange: 1 | 2 | 3 | 4 | 5;
  commissionRate: number;
}

export type VenueType =
  | 'restaurant'
  | 'cafe'
  | 'bar'
  | 'hotel'
  | 'cinema'
  | 'park'
  | 'museum'
  | 'theater'
  | 'club';

export interface Match {
  id: string;
  user1Id: string;
  user2Id: string;
  createdAt: Date;
  lastMessageAt?: Date;
  status: 'active' | 'archived' | 'blocked';
}

export interface Message {
  id: string;
  matchId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'location' | 'date_proposal';
  createdAt: Date;
  isRead: boolean;
}

export interface Date {
  id: string;
  matchId: string;
  venueId?: string;
  scheduledAt: Date;
  status: 'pending' | 'accepted' | 'declined' | 'cancelled' | 'completed';
  details?: {
    note?: string;
    preferences?: string[];
    customLocation?: string;
  };
}

export interface Transaction {
  id: string;
  userId: string;
  venueId?: string;
  dateId?: string;
  amount: number;
  commissionAmount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  details?: any;
  createdAt: Date;
}

export interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'moderator' | 'support';
  permissions: string[];
  createdAt: Date;
}

export interface PartnerUser {
  id: string;
  email: string;
  companyName: string;
  venues: string[];
  commissionRate: number;
  balance: number;
  status: 'active' | 'pending' | 'suspended';
  createdAt: Date;
}

// Дополнительные типы из shared/types.ts
export interface KeyPair {
  publicKey: string;
  privateKey: string;
}

export interface EncryptedMessage {
  ciphertext: string;
  iv: string;
  key?: string;
}

export interface Analytics {
  track(event: string, properties?: Record<string, any>): void;
}

export type AppointmentStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled';

export interface Appointment {
  id: string;
  status: AppointmentStatus;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  partnerId: string;
  duration: number;
  serviceId: string;
  dateTime: Date;
  price: number;
  reviewId?: string;
  notes?: string;
}
