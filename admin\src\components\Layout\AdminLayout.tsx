import React, { useState } from 'react';
import { Layout, Menu, <PERSON>readcrumb, Avatar, Dropdown, Badge, theme } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  BellOutlined,
  MessageOutlined,
  SafetyOutlined,
  FileTextOutlined,
  TeamOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  GlobalOutlined,
  LineChartOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import './AdminLayout.css';

const { Header, Content, Footer, Sider } = Layout;

const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { logout, user } = useAuth();
  const { token } = theme.useToken();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">Профиль</Link>
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        <Link to="/settings">Настройки</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        Выход
      </Menu.Item>
    </Menu>
  );

  const notificationsMenu = (
    <Menu>
      <Menu.Item key="notification1">
        <span>Новый отчет о модерации</span>
        <div style={{ fontSize: '12px', color: token.colorTextSecondary }}>2 часа назад</div>
      </Menu.Item>
      <Menu.Item key="notification2">
        <span>Обновление системы</span>
        <div style={{ fontSize: '12px', color: token.colorTextSecondary }}>1 день назад</div>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="all">
        <Link to="/notifications">Все уведомления</Link>
      </Menu.Item>
    </Menu>
  );

  const getBreadcrumbItems = () => {
    const paths = pathname.split('/').filter(Boolean);
    return [
      { title: <Link to="/">Главная</Link>, key: 'home' },
      ...paths.map((path, index) => {
        const url = `/${paths.slice(0, index + 1).join('/')}`;
        return {
          title: <Link to={url}>{path.charAt(0).toUpperCase() + path.slice(1)}</Link>,
          key: path,
        };
      }),
    ];
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        trigger={null}
        width={256}
        style={{
          boxShadow: '2px 0 8px 0 rgba(29,35,41,.05)',
          zIndex: 10,
        }}
      >
        <div className="logo">
          {!collapsed ? 'Likes-Love Admin' : 'LL'}
        </div>
        <Menu
          theme="dark"
          selectedKeys={[pathname]}
          mode="inline"
          items={[
            {
              key: '/',
              icon: <DashboardOutlined />,
              label: <Link to="/">Дашборд</Link>,
            },
            {
              key: '/users',
              icon: <UserOutlined />,
              label: <Link to="/users">Пользователи</Link>,
            },
            {
              key: '/messages',
              icon: <MessageOutlined />,
              label: <Link to="/messages">Сообщения</Link>,
            },
            {
              key: '/moderation',
              icon: <SafetyOutlined />,
              label: <Link to="/moderation">Модерация</Link>,
            },
            {
              key: 'referral',
              icon: <TeamOutlined />,
              label: 'Реферальная программа',
              children: [
                {
                  key: 'referral-program',
                  label: <Link to="/referral-program">Программа</Link>,
                },
                {
                  key: 'referral-statistics',
                  label: <Link to="/referral-statistics">Статистика</Link>,
                },
              ],
            },
            {
              key: 'settings',
              icon: <SettingOutlined />,
              label: <Link to="/settings">Настройки</Link>,
            },
          ]}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: token.colorBgContainer,
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center'
        }}>
          <Breadcrumb style={{ margin: '0 16px' }}>
            {getBreadcrumbItems().map((item) => (
              <Breadcrumb.Item key={item.key}>{item.title}</Breadcrumb.Item>
            ))}
          </Breadcrumb>
          <div className="header-actions">
            <Dropdown overlay={notificationsMenu} placement="bottomRight">
              <Badge count={2} dot>
                <BellOutlined style={{ fontSize: 20 }} />
              </Badge>
            </Dropdown>
            <Dropdown overlay={userMenu} placement="bottomRight">
              <Avatar style={{ margin: '0 16px' }} src={user.avatarUrl || 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png'} />
            </Dropdown>
          </div>
        </Header>
        <Content style={{ margin: '16px' }}>
          <div style={{ padding: 24, minHeight: 360, background: token.colorBgContainer }}>
            {children}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          Likes Love Admin Panel {new Date().getFullYear()}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;


