import {
  User,
  UserProfile,
  Match,
  Message,
  Notification,
  PartnerUser,
  KeyPair,
  EncryptedMessage,
  Appointment,
  AppointmentStatus
} from '../types';

describe('Shared Types Validation', () => {
  describe('User Types', () => {
    it('should validate User interface structure', () => {
      const mockUser: User = {
        id: 'user-123',
        email: '<EMAIL>',
        username: 'testuser',
        profile: {
          id: 'profile-123',
          userId: 'user-123',
          firstName: 'Test',
          lastName: 'User',
          age: 25,
          bio: 'Test bio',
          photos: ['photo1.jpg'],
          location: {
            latitude: 40.7128,
            longitude: -74.0060,
            city: 'New York',
            country: 'USA'
          },
          preferences: {
            ageRange: { min: 18, max: 35 },
            maxDistance: 50,
            interestedIn: 'both'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        },
        isActive: true,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      expect(mockUser.id).toBeDefined();
      expect(mockUser.email).toContain('@');
      expect(mockUser.profile.age).toBeGreaterThan(0);
      expect(mockUser.profile.location.latitude).toBeGreaterThanOrEqual(-90);
      expect(mockUser.profile.location.latitude).toBeLessThanOrEqual(90);
      expect(mockUser.profile.location.longitude).toBeGreaterThanOrEqual(-180);
      expect(mockUser.profile.location.longitude).toBeLessThanOrEqual(180);
    });

    it('should validate UserProfile preferences', () => {
      const mockProfile: UserProfile = {
        id: 'profile-123',
        userId: 'user-123',
        firstName: 'Test',
        lastName: 'User',
        age: 25,
        bio: 'Test bio',
        photos: ['photo1.jpg'],
        location: {
          latitude: 40.7128,
          longitude: -74.0060,
          city: 'New York',
          country: 'USA'
        },
        preferences: {
          ageRange: { min: 18, max: 35 },
          maxDistance: 50,
          interestedIn: 'both'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      expect(mockProfile.preferences.ageRange.min).toBeLessThanOrEqual(
        mockProfile.preferences.ageRange.max
      );
      expect(mockProfile.preferences.maxDistance).toBeGreaterThan(0);
      expect(['male', 'female', 'both']).toContain(mockProfile.preferences.interestedIn);
    });
  });

  describe('Match and Message Types', () => {
    it('should validate Match interface', () => {
      const mockMatch: Match = {
        id: 'match-123',
        user1Id: 'user-1',
        user2Id: 'user-2',
        status: 'active',
        createdAt: new Date(),
        lastMessageAt: new Date()
      };

      expect(mockMatch.user1Id).not.toBe(mockMatch.user2Id);
      expect(['pending', 'active', 'blocked', 'expired']).toContain(mockMatch.status);
    });

    it('should validate Message interface', () => {
      const mockMessage: Message = {
        id: 'message-123',
        matchId: 'match-123',
        senderId: 'user-1',
        content: 'Hello!',
        type: 'text',
        isRead: false,
        createdAt: new Date()
      };

      expect(mockMessage.content).toBeTruthy();
      expect(['text', 'image', 'gif', 'sticker']).toContain(mockMessage.type);
      expect(typeof mockMessage.isRead).toBe('boolean');
    });
  });

  describe('Partner and Appointment Types', () => {
    it('should validate PartnerUser interface', () => {
      const mockPartner: PartnerUser = {
        id: 'partner-123',
        email: '<EMAIL>',
        companyName: 'Test Company',
        venues: ['venue-1', 'venue-2'],
        commissionRate: 0.15,
        balance: 1000.50,
        status: 'active',
        createdAt: new Date()
      };

      expect(mockPartner.commissionRate).toBeGreaterThan(0);
      expect(mockPartner.commissionRate).toBeLessThanOrEqual(1);
      expect(mockPartner.balance).toBeGreaterThanOrEqual(0);
      expect(['active', 'pending', 'suspended']).toContain(mockPartner.status);
    });

    it('should validate Appointment interface', () => {
      const mockAppointment: Appointment = {
        id: 'appointment-123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'user-123',
        partnerId: 'partner-123',
        duration: 60,
        serviceId: 'service-123',
        dateTime: new Date(),
        price: 100.00
      };

      expect(mockAppointment.duration).toBeGreaterThan(0);
      expect(mockAppointment.price).toBeGreaterThan(0);
      expect(['pending', 'confirmed', 'completed', 'cancelled']).toContain(mockAppointment.status);
    });
  });

  describe('Security Types', () => {
    it('should validate KeyPair interface', () => {
      const mockKeyPair: KeyPair = {
        publicKey: 'public-key-string',
        privateKey: 'private-key-string'
      };

      expect(mockKeyPair.publicKey).toBeTruthy();
      expect(mockKeyPair.privateKey).toBeTruthy();
      expect(mockKeyPair.publicKey).not.toBe(mockKeyPair.privateKey);
    });

    it('should validate EncryptedMessage interface', () => {
      const mockEncryptedMessage: EncryptedMessage = {
        ciphertext: 'encrypted-content',
        iv: 'initialization-vector',
        key: 'optional-key'
      };

      expect(mockEncryptedMessage.ciphertext).toBeTruthy();
      expect(mockEncryptedMessage.iv).toBeTruthy();
    });
  });

  describe('Notification Types', () => {
    it('should validate Notification interface', () => {
      const mockNotification: Notification = {
        id: 'notification-123',
        userId: 'user-123',
        type: 'match',
        title: 'New Match!',
        message: 'You have a new match',
        isRead: false,
        createdAt: new Date()
      };

      expect(['match', 'message', 'like', 'super_like', 'appointment', 'system']).toContain(
        mockNotification.type
      );
      expect(typeof mockNotification.isRead).toBe('boolean');
      expect(mockNotification.title).toBeTruthy();
    });
  });
});
