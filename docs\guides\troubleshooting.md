# Руководство по устранению проблем

*Последнее обновление: [текущая дата]*

## Содержание
- [Общие проблемы](#общие-проблемы)
- [Backend проблемы](#backend-проблемы)
- [Frontend проблемы](#frontend-проблемы)
- [Mobile проблемы](#mobile-проблемы)
- [CI/CD проблемы](#cicd-проблемы)
- [Мониторинг и логи](#мониторинг-и-логи)

## Общие проблемы

### Проблемы с установкой

#### Конфликты зависимостей
```bash
# Очистка кэша npm
npm cache clean --force

# Удаление node_modules
npm run clean

# Переустановка с legacy-peer-deps
npm install --legacy-peer-deps
```

#### Проблемы с Python/node-gyp
```bash
# Windows
npm install --global windows-build-tools

# Переустановка node-gyp
npm install -g node-gyp
npm config set python python2.7
```

### Проблемы с Docker

#### Проблемы с портами
```bash
# Проверка занятых портов
netstat -ano | findstr "LISTENING"

# Остановка всех контейнеров
docker-compose down
```

#### Проблемы с правами
```bash
# Перезапуск Docker Desktop
net stop com.docker.service
net start com.docker.service
```

## Backend проблемы

### База данных

#### Ошибки миграции
```bash
# Сброс базы данных
npm run db:reset

# Пересоздание миграций
npm run prisma:migrate:reset
```

#### Проблемы с подключением
```bash
# Проверка подключения
npm run db:check

# Просмотр логов
docker-compose logs postgres
```

### API ошибки

#### CORS ошибки
- Проверьте настройки в `security.config.ts`
- Убедитесь, что домены совпадают
- Проверьте заголовки запросов

#### Авторизация
```bash
# Очистка токенов
npm run auth:clear

# Проверка JWT
npm run jwt:verify <token>
```

## Frontend проблемы

### Сборка

#### TypeScript ошибки
```bash
# Проверка типов
npm run type-check

# Очистка кэша
npm run clean:cache
```

#### Webpack ошибки
```bash
# Очистка кэша
npm run clean:webpack

# Отладочная сборка
npm run build:debug
```

### Runtime ошибки

#### Белый экран
- Проверьте консоль браузера
- Проверьте
