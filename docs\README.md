# Документация Likes-Love.com

## Структура документации

### 📁 guides/
Основные руководства по разработке и эксплуатации

#### development/
- `setup.md` - Настройка окружения
- `development.md` - Руководство по разработке
- `code-style.md` - Правила оформления кода
- `git-workflow.md` - Работа с Git

#### testing/
- `testing-guide.md` - Полное руководство по тестированию
- `unit-testing.md` - Модульное тестирование
- `e2e-testing.md` - E2E тестирование
- `mobile-testing.md` - Тестирование мобильных приложений
- `performance-testing.md` - Нагрузочное тестирование

#### deployment/
- `deployment-guide.md` - Общее руководство по развертыванию
- `rustore-deployment.md` - Развертывание в RuStore
- `yandex-cloud.md` - Развертывание в Яндекс.Облаке
- `ci-cd.md` - Настройка CI/CD

#### troubleshooting/
- `troubleshooting.md` - Устранение проблем
- `faq.md` - Часто задаваемые вопросы
- `known-issues.md` - Известные проблемы

### 📁 api/
Документация по API

- `api-reference.md` - Справочник API
- `api-examples.md` - Примеры использования API
- `websocket.md` - WebSocket API

### 📁 architecture/
Архитектурная документация

- `architecture-overview.md` - Обзор архитектуры
- `database-schema.md` - Схема базы данных
- `components.md` - Компоненты системы

### 📁 security/
Документация по безопасности

- `security-guide.md` - Руководство по безопасности
- `authentication.md` - Аутентификация
- `authorization.md` - Авторизация

### 📁 admin/
Документация для администраторов

- `admin-guide.md` - Руководство администратора
- `monitoring.md` - Мониторинг системы
- `backup.md` - Резервное копирование

## Основные файлы

- `README.md` - Этот файл
- `CHANGELOG.md` - История изменений
- `CONTRIBUTING.md` - Руководство по внесению изменений
- `LICENSE.md` - Лицензия

## Структура проекта

```
likes-love.com/
├── backend/           # Backend на Node.js
│   ├── src/          # Исходный код
│   ├── prisma/       # Схема и миграции базы данных
│   └── tests/        # Тесты
├── web/              # Frontend на React
│   ├── src/          # Исходный код
│   ├── public/       # Статические файлы
│   └── tests/        # Тесты
├── mobile/           # Мобильное приложение
│   ├── android/      # Android-специфичный код
│   ├── ios/          # iOS-специфичный код
│   └── src/          # Общий код
├── docs/             # Документация
└── scripts/          # Скрипты для разработки и деплоя
```

