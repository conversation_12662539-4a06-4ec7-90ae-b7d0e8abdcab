# Руководство по тестированию

*Последнее обновление: [текущая дата]*

## Содержание
- [Типы тестов](#типы-тестов)
- [Инструменты тестирования](#инструменты-тестирования)
- [Запуск тестов](#запуск-тестов)
- [Написание тестов](#написание-тестов)
- [Непрерывная интеграция](#непрерывная-интеграция)
- [Отчеты и метрики](#отчеты-и-метрики)
- [Тестирование безопасности](#тестирование-безопасности)

## Типы тестов

### 1. Модульные тесты
Тестирование отдельных компонентов и функций.

### 2. Интеграционные тесты
Тестирование взаимодействия между компонентами.

### 3. E2E тесты
Тестирование полного пользовательского сценария.

### 4. Нагрузочные тесты
Тестирование производительности под нагрузкой.

## Инструменты тестирования

### Backend
- Jest - для модульных и интеграционных тестов
- Supertest - для тестирования API
- k6 - для нагрузочного тестирования

### Frontend
- Jest - для модульных тестов
- React Testing Library - для тестирования компонентов
- Cypress - для E2E тестирования

### Mobile
- Jest - для модульных тестов
- Detox - для E2E тестирования на мобильных устройствах

## Запуск тестов

### Модульные тесты
```bash
# Запуск всех модульных тестов
npm run test:unit

# Запуск тестов с наблюдением за изменениями
npm run test:unit:watch

# Запуск тестов для конкретного файла
npm run test:unit -- path/to/test.spec.js
```

### Интеграционные тесты
```bash
# Запуск всех интеграционных тестов
npm run test:integration

# Запуск тестов для конкретного API
npm run test:integration -- --testPathPattern=api/users
```

### E2E тесты
```bash
# Запуск всех E2E тестов
npm run test:e2e

# Запуск E2E тестов в интерактивном режиме
npm run test:e2e:open
```

### Нагрузочные тесты
```bash
# Запуск нагрузочных тестов
npm run test:load

# Запуск стресс-тестов
npm run test:stress

# Запуск тестов производительности
npm run test:performance
```

## Написание тестов

### Модульные тесты
```javascript
describe('User Service', () => {
  it('should create a new user', async () => {
    const user = await userService.create({
      email: '<EMAIL>',
      password: 'password123'
    });
    expect(user).toHaveProperty('id');
    expect(user.email).toBe('<EMAIL>');
  });
});
```

### Интеграционные тесты
```javascript
describe('User API', () => {
  it('should return user profile', async () => {
    const response = await request(app)
      .get('/api/users/1')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('email');
  });
});
```

### E2E тесты
```javascript
describe('Login Flow', () => {
  it('should login successfully', () => {
    cy.visit('/login');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    cy.url().should('include', '/dashboard');
  });
});
```

## Непрерывная интеграция

### GitHub Actions

#### Проверка конфигурации
```bash
# Локальная проверка
act -n

# Запуск конкретного workflow
act push
```

#### Просмотр результатов
- Откройте GitHub Actions
- Выберите workflow
- Проверьте артефакты и логи

### Pre-commit хуки

```bash
# Установка хуков
npm run prepare

# Проверка хуков
npm run test:hooks
```

## Отчеты и метрики

### Генерация отчетов

#### Покрытие кода
```bash
npm run coverage:report
```

#### Производительность
```bash
npm run performance:report
```

### Мониторинг

#### Метрики
```bash
# Сбор метрик
npm run metrics:collect

# Генерация отчета
npm run metrics:report
```

#### Логи
```bash
# Просмотр логов тестов
npm run logs:test

# Архивация логов
npm run logs:archive
```

## Тестирование безопасности

### Статический анализ

#### Проверка зависимостей
```bash
npm audit

# Исправление уязвимостей
npm audit fix
```

#### Анализ кода
```bash
npm run security:check
```

### Динамический анализ

#### OWASP ZAP
```bash
npm run security:zap
```

#### Penetration testing
```bash
npm run security:pentest
```

## Дополнительные ресурсы
- [Руководство по разработке](./development.md)
- [Руководство по устранению проблем](./troubleshooting.md)
- [Документация по безопасности](../security/overview.md)
