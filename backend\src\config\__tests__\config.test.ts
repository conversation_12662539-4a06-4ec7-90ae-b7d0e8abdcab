import { config } from '../index';

describe('Configuration Security Tests', () => {
  beforeEach(() => {
    // Сброс переменных окружения перед каждым тестом
    delete process.env.JWT_SECRET;
    delete process.env.CORS_ORIGIN;
    delete process.env.NODE_ENV;
  });

  afterEach(() => {
    // Восстановление переменных окружения после каждого теста
    process.env.NODE_ENV = 'test';
  });

  describe('JWT Configuration', () => {
    it('should throw error in production without JWT_SECRET', () => {
      process.env.NODE_ENV = 'production';
      
      expect(() => {
        // Перезагружаем модуль для применения новых переменных окружения
        jest.resetModules();
        require('../index');
      }).toThrow('JWT_SECRET must be set in production environment');
    });

    it('should use default JWT secret in development with warning', () => {
      process.env.NODE_ENV = 'development';
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      jest.resetModules();
      const { config: devConfig } = require('../index');
      
      expect(devConfig.jwt.secret).toBe('dev-secret-key-change-in-production');
      expect(consoleSpy).toHaveBeenCalledWith(
        '⚠️  WARNING: Using default JWT secret in development. Set JWT_SECRET environment variable.'
      );
      
      consoleSpy.mockRestore();
    });

    it('should use custom JWT secret when provided', () => {
      process.env.JWT_SECRET = 'custom-super-secure-secret';
      
      jest.resetModules();
      const { config: customConfig } = require('../index');
      
      expect(customConfig.jwt.secret).toBe('custom-super-secure-secret');
    });
  });

  describe('CORS Configuration', () => {
    it('should throw error in production without CORS_ORIGIN', () => {
      process.env.NODE_ENV = 'production';
      
      expect(() => {
        jest.resetModules();
        require('../index');
      }).toThrow('CORS_ORIGIN must be explicitly set in production environment');
    });

    it('should use default CORS origins in development with warning', () => {
      process.env.NODE_ENV = 'development';
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      jest.resetModules();
      const { config: devConfig } = require('../index');
      
      expect(devConfig.corsOrigin).toEqual([
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:8080'
      ]);
      expect(consoleSpy).toHaveBeenCalledWith(
        '⚠️  WARNING: Using permissive CORS in development. Set CORS_ORIGIN environment variable.'
      );
      
      consoleSpy.mockRestore();
    });

    it('should use custom CORS origin when provided', () => {
      process.env.CORS_ORIGIN = 'https://likes-love.com';
      
      jest.resetModules();
      const { config: customConfig } = require('../index');
      
      expect(customConfig.corsOrigin).toBe('https://likes-love.com');
    });
  });

  describe('Database Configuration', () => {
    it('should have database URL configuration', () => {
      expect(config.database).toBeDefined();
      expect(config.database.url).toBeDefined();
    });

    it('should have proper pool configuration', () => {
      expect(config.database.pool).toBeDefined();
      expect(config.database.pool.min).toBeGreaterThanOrEqual(1);
      expect(config.database.pool.max).toBeGreaterThan(config.database.pool.min);
    });
  });

  describe('Security Configuration', () => {
    it('should have secure bcrypt rounds', () => {
      expect(config.security.bcryptRounds).toBeGreaterThanOrEqual(10);
    });

    it('should have rate limiting configuration', () => {
      expect(config.rateLimit).toBeDefined();
      expect(config.rateLimit.maxRequests).toBeGreaterThan(0);
      expect(config.rateLimit.window).toBeDefined();
    });
  });

  describe('Environment Variables Validation', () => {
    it('should validate required environment variables', () => {
      const requiredVars = [
        'DATABASE_URL',
        'REDIS_URL'
      ];

      requiredVars.forEach(varName => {
        if (process.env[varName]) {
          expect(process.env[varName]).toBeTruthy();
        }
      });
    });
  });
});
