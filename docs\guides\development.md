# Руководство по разработке

*Последнее обновление: [текущая дата]*

## Содержание
- [Настройка окружения](#настройка-окружения)
- [Структура проекта](#структура-проекта)
- [Рабочий процесс](#рабочий-процесс)
- [Линтинг и форматирование](#линтинг-и-форматирование)
- [Сборка](#сборка)
- [Отладка](#отладка)

## Настройка окружения

### Требования
- Node.js 16.x или выше
- npm 8.x или выше
- Docker и Docker Compose
- Git

### Установка зависимостей
```bash
# Клонирование репозитория
git clone https://github.com/your-org/likes-love.git
cd likes-love

# Установка зависимостей
npm install
```

## Структура проекта

Подробная структура проекта описана в [документе о структуре проекта](../project-structure-detailed.md).

## Рабочий процесс

### Ветвление
- `main` - стабильная версия
- `develop` - текущая разработка
- `feature/*` - новые функции
- `bugfix/*` - исправления ошибок
- `release/*` - подготовка релиза

### Коммиты
Используйте [Conventional Commits](https://www.conventionalcommits.org/):
```
feat: добавлена новая функция
fix: исправлена ошибка
docs: обновлена документация
style: изменения форматирования
refactor: рефакторинг кода
test: добавлены тесты
chore: обновление зависимостей
```

## Линтинг и форматирование

### Проверка кода
```bash
# Проверка всего проекта
npm run lint

# Проверка отдельных частей
npm run lint:backend
npm run lint:web
npm run lint:mobile
```

### Форматирование
```bash
# Форматирование всего проекта
npm run format

# Форматирование отдельных частей
npm run format:backend
npm run format:web
npm run format:mobile
```

## Сборка

### Production сборка

#### Backend
```bash
npm run build:backend
```

#### Frontend
```bash
npm run build:web
```

#### Mobile
```bash
# Android
npm run build:android

# iOS
npm run build:ios
```

## Отладка

### Backend
1. Запустите сервер в режиме отладки:
```bash
npm run debug:backend
```

2. Подключитесь к отладчику:
- Откройте Chrome DevTools
- Перейдите на chrome://inspect
- Найдите и подключитесь к процессу Node.js

### Frontend
1. Откройте Chrome DevTools
2. Перейдите во вкладку "Sources"
3. Найдите исходный код в webpack://

### Mobile
1. Откройте Chrome DevTools
2. Перейдите на chrome://inspect
3. Найдите ваше устройство/эмулятор

## Дополнительные ресурсы
- [Руководство по тестированию](./testing.md)
- [Руководство по устранению проблем](./troubleshooting.md)
- [Руководство по развертыванию](../deployment/deployment.md)
